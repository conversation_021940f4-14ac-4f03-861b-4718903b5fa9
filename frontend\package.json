{"name": "pptist", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "author": "<EMAIL>", "homepage": "https://github.com/pipipi-pikachu/PPTist", "dependencies": {"@icon-park/vue-next": "^1.4.2", "animate.css": "^4.1.1", "axios": "^1.7.9", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dexie": "^4.0.11", "echarts": "^5.5.1", "file-saver": "^2.0.5", "hfmath": "^0.0.2", "html-to-image": "^1.11.13", "lodash": "^4.17.21", "mitt": "^3.0.1", "nanoid": "^5.0.7", "number-precision": "^1.6.0", "pinia": "^3.0.2", "pptxgenjs": "^3.12.0", "pptxtojson": "^1.5.2", "prosemirror-commands": "^1.6.0", "prosemirror-dropcursor": "^1.8.1", "prosemirror-gapcursor": "^1.3.2", "prosemirror-history": "^1.3.2", "prosemirror-inputrules": "^1.4.0", "prosemirror-keymap": "^1.2.2", "prosemirror-model": "^1.22.2", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.4.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.33.9", "svg-arc-to-cubic-bezier": "^3.2.0", "svg-pathdata": "^7.1.0", "tinycolor2": "^1.6.0", "tippy.js": "^6.3.7", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuedraggable": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@rushstack/eslint-patch": "^1.3.3", "@tsconfig/node18": "^18.2.2", "@types/crypto-js": "^4.2.1", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/node": "^18.19.3", "@types/svg-arc-to-cubic-bezier": "^3.2.2", "@types/tinycolor2": "^1.4.6", "@vitejs/plugin-vue": "^5.1.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "husky": "^8.0.3", "npm-run-all2": "^6.1.1", "sass": "1.69.6", "typescript": "~5.3.0", "vite": "^5.3.5", "vue-tsc": "^2.0.29"}}