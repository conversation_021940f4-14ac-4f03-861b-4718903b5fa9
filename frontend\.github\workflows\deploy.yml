name: deploy

on:
  push:
    branches: [master]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Clear npm cache
        run: npm cache clean --force

      - uses: actions/checkout@v3

      - name: Use Node.js v18
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install and Build
        run: |
          npm install
          npm run build

      - name: Deploy to github pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.PPTIST }}
          commit_message: deploy to github pages
          publish_dir: ./dist