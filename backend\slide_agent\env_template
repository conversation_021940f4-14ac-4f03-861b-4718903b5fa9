# 根据slide/agentconfig.py下的config使用哪个模型，就配置哪个key
GOOGLE_API_KEY=xx
DEEPSEEK_API_KEY=xx
OPENAI_API_KEY=xx
CLAUDE_API_KEY=xx
# 阿里云api
ALI_API_KEY=xx
#流式的响应，多Agent的中每个Agent的流式响应需要更多测试，因为split_topic agent要求json格式结果解析，流式有问题
STREAMING=false

# 是否使用代理
# HTTP_PROXY=http://127.0.0.1:7890
# HTTPS_PROXY=http://127.0.0.1:7890

# Pexels API配置
PEXELS_API_KEY=xx

# 获取Pexels API密钥的步骤：
# 1. 访问 https://www.pexels.com/api/
# 2. 注册账号并申请API密钥
# 3. 将获得的API密钥填入此处