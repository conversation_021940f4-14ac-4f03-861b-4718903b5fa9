.ProseMirror, .ProseMirror-static {
  outline: 0;
  border: 0;
  font-size: 16px;
  word-break: break-word;
  white-space: normal;

  &:not(.ProseMirror-static) {
    user-select: text;
  }

  ::selection {
    background-color: rgba($themeColor, 0.25);
    color: inherit;
  }

  p {
    margin: 0;
    margin-top: var(--paragraphSpace);
  }
  p:first-child {
    margin-top: 0;
  }

  ul, ol, li {
    margin: 0;
    margin-top: var(--paragraphSpace);
  }
  ul {
    list-style-type: disc;
    padding-inline-start: 1.25em;

    li {
      list-style-type: inherit;
      padding: 0.125em 0;
    }
  }

  ol {
    list-style-type: decimal;
    padding-inline-start: 1.25em;

    li {
      list-style-type: inherit;
      padding: 0.125em 0;
    }
  }

  code {
    background-color: #f1f1f1;
    padding: 2px 6px;
    margin: 0 1px;
    border-radius: 4px;
    font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, monospace;
  }

  sup {
    vertical-align: super;
    font-size: smaller;
  }
  sub {
    vertical-align: sub;
    font-size: smaller;
  }

  blockquote {
    overflow: hidden;
    padding: 0 1.2em;
    margin: 0.6em 0;
    font-style: italic;
    border-left: 4px solid #e0e0e0;
  }

  [data-indent='1'] {
    padding-left: 1em;
  }
  [data-indent='2'] {
    padding-left: 2em;
  }
  [data-indent='3'] {
    padding-left: 3em;
  }
  [data-indent='4'] {
    padding-left: 4em;
  }
  [data-indent='5'] {
    padding-left: 5em;
  }
  [data-indent='6'] {
    padding-left: 6em;
  }
  [data-indent='7'] {
    padding-left: 7em;
  }
  [data-indent='8'] {
    padding-left: 8em;
  }
}

.ProseMirror-selectednode {
  outline: none !important;
}