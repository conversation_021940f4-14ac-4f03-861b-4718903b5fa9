export const FONTS = [
  { label: '默认字体', value: '' },
  { label: '思源黑体', value: 'SourceHanSans' },
  { label: '思源宋体', value: 'SourceHanSerif' },
  { label: '方正黑体', value: 'FangZhengHeiTi' },
  { label: '方正楷体', value: 'FangZhengKaiTi' },
  { label: '方正宋体', value: 'FangZhengShuSong' },
  { label: '方正仿宋', value: 'FangZhengFangSong' },
  { label: '阿里巴巴普惠体', value: 'AlibabaPuHuiTi' },
  { label: '朱雀仿宋', value: 'ZhuqueFangSong' },
  { label: '霞鹜文楷', value: 'LXGWWenKai' },
  { label: '文鼎PL楷体', value: 'WenDingPLKaiTi' },
  { label: '得意黑', value: 'DeYiHei' },
  { label: 'MiSans', value: 'MiSans' },
  { label: '仓耳小丸子', value: 'CangerXiaowanzi' },
  { label: '优设标题黑', value: 'YousheTitleBlack' },
  { label: '峰广明锐体', value: 'FengguangMingrui' },
  { label: '摄图摩登小方体', value: 'ShetuModernSquare' },
  { label: '站酷快乐体', value: 'ZcoolHappy' },
  { label: '字制区喜脉体', value: 'ZizhiQuXiMai' },
  { label: '素材集市康康体', value: 'SucaiJishiKangkang' },
  { label: '素材集市酷方体', value: 'SucaiJishiCoolSquare' },
  { label: '途牛类圆体', value: 'TuniuRounded' },
  { label: '锐字真言体', value: 'RuiziZhenyan' },
]