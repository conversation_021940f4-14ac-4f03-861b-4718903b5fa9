# 谷歌Gemini模型
GOOGLE_API_KEY=xxx
DEEPSEEK_API_KEY=xxx
ALI_API_KEY=sk-xxx
OPENAI_API_KEY=xxx
CLAUDE_API_KEY=xxx
# 使用LLM的流式的响应，测试gemini的效果很好，true表示开启流的请求和响应
STREAMING=true
# 使用哪个模型供应商, ollama, 或者vllm，需要提供OLLAMA_API_URL， VLLM_API_URL
#MODEL_PROVIDER=google
# 使用哪个模型
#LLM_MODEL=gemini-2.0-flash

MODEL_PROVIDER=deepseek
LLM_MODEL=deepseek-chat
# 是否使用代理，clash的代理7890
# HTTP_PROXY=http://127.0.0.1:7890
# HTTPS_PROXY=http://127.0.0.1:7890