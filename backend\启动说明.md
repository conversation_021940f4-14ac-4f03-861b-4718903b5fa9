# TrainPPTAgent 后端启动说明

## 概述

本项目提供了两个Python脚本来启动整个后端服务：

1. **`start_backend.py`** - 完整版启动脚本

## 快速开始

### 使用完整版启动脚本

```bash
cd backend
python start_backend.py
```

**功能特性：**
- ✅ 自动检查Python版本
- ✅ 自动安装依赖包
- ✅ 端口占用检测和清理（需要用户确认）
- ✅ 自动设置环境文件
- ✅ 多进程管理和监控
- ✅ 优雅的服务停止

## 服务说明

启动脚本会自动启动以下三个服务：

| 服务名称 | 端口 | 目录 | 说明 |
|---------|------|------|------|
| main_api | 6800 | `main_api/` | 主API服务 |
| simpleOutline | 10001 | `simpleOutline/` | 大纲生成服务 |
| slide_agent | 10011 | `slide_agent/` | PPT内容生成服务 |

## 环境配置

### 自动环境文件设置

启动脚本会自动检查并复制环境模板文件：

1. **main_api**: `main_api/env_template` → `main_api/.env`
2. **simpleOutline**: `simpleOutline/env_template` → `simpleOutline/.env`
3. **slide_agent**: `slide_agent/env_template` → `slide_agent/.env`

### 手动配置API密钥

启动前，请确保在相应的 `.env` 文件中配置了必要的API密钥：

```bash
# 编辑环境文件
cd backend/main_api
cp env_template .env
# 编辑 .env 文件，填入你的API密钥

cd ../simpleOutline
cp env_template .env
# 编辑 .env 文件，填入你的API密钥

cd ../slide_agent
cp env_template .env
# 编辑 .env 文件，填入你的API密钥
```

## 端口清理

如果遇到端口被占用的情况：

### 自动清理（完整版脚本）
```bash
python start_backend.py
# 脚本会自动检测端口占用并询问是否清理
```

### 手动清理
```bash
# 查看端口占用
lsof -i :6800
lsof -i :10001
lsof -i :10011

# 终止占用进程
kill -9 <PID>
```

## 停止服务

### 方法一：Ctrl+C
在启动脚本运行的控制台中按 `Ctrl+C`

### 方法二：查找并终止进程
```bash
# 查找Python进程
ps aux | grep python

# 终止相关进程
kill -9 <PID>
```

## 日志查看

### 实时日志
启动脚本会显示各个服务的启动状态和错误信息。

### 服务日志
各个服务的日志会输出到控制台，可以通过重定向保存：

```bash
python start_backend.py > backend.log 2>&1
```



