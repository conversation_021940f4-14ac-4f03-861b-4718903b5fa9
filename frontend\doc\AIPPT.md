## AIPPT的基本原理
1. 定义PPT结构（一套PPT中都有什么类型的页面，每种页面都有些什么内容）；
2. 基于以上结构，定义数据格式，该数据将用于AI生成结构化的PPT数据，具体结构见：
    - 示例数据：`public/mocks/AIPPT.json`
    - 结构定义：`src/types/AIPPT.ts`
3. 制作模板，模板中标记好结构类型；
4. AI生成符合第1步定义的PPT结构的数据；
5. 利用AI或其他方案，生成相关的配图（常见途径有：AI文生图、图库搜索匹配）；
6. 将AI生成的数据、配图与模板进行匹配结合，生成最终的PPT。

> 注：虽然当前线上版本不提供配图演示效果，但是AIPPT的方法是支持此功能的，你只需要自己提供图片源，按照要求的格式将待选图片集合传入AIPPT方法即可。

## AIPPT模板制作流程
1. 打开PPTist；
2. 制作模板页面；
3. 打开左上角菜单[幻灯片类型标注]功能；
4. 为制作好的页面标注页面类型和节点类型；
5. 使用导出功能导出为JSON文件。

> 注意：实际上并不存在专门提供给AIPPT的模板。所谓的AIPPT模板只是把在PPTist中制作的普通页面标注上类型标记而已。这些数据不仅仅用于AI生成PPT，也可以作为普通的页面模板使用。

## 模板标记类型：页面标记和节点标记
#### 封面页
* 标题
* 正文
* 图片（背景图、页面插图）
#### 目录页
* 目录标题（标记类型为：列表项目）
* 图片（背景图、页面插图）
#### 过渡页（章节过渡）
* 标题
* 正文
* 节编号
* 图片（背景图、页面插图）
#### 内容页
* 标题
* 2～4个内容项，包括：
  * 内容项标题（标记类型为：列表项标题）
  * 内容项正文（标记类型为：列表项目）
  * 内容项编号（标记类型为：项目编号）
* 图片（背景图、页面插图、项目插图）
#### 结束页（致谢页）
* 图片（背景图、页面插图）

> 节点标记分为两种 - 文本标记和图片标记：
> - 文本标记可作用于文本节点和带文字的形状节点；
> - 图片标记只作用于图片节点；
> - 你可以自行添加更多类型的标记（如图表）。

## AIPPT模板制作原则
一个用于AIPPT的模板至少包括以下页面（共12页）：
* 1个封面页
* 6个目录页（2～6个目录项各1个，10个目录项的1个）
* 1个过渡页
* 3个内容页（2～4个内容项各1个）
* 1个结束页

> 注意：
> 1. 以上页数仅满足当前替换逻辑的最基本要求，如果希望AI生成的PPT具有一定的随机性，需要适当增加每种页面的数量（举个例子，假设模板中存在3个封面页，生成时会从3个中随机选择1个使用）；
> 2. 当前替换逻辑下，目录页可支持1～20个目录项，内容页可支持1～12个内容项，但不需要每种数量的模板都做一遍，因为程序会自动通过模板的拼接/裁减方式来实现特殊的项目数；
> 3. 你可以自行调整替换逻辑，以支持更多情况。