# MultiAgentPPT：简单示例用于生成PPT大纲

本项目结合了 **A2A 框架** 与 **Google ADK 架构**，通过 SSE（Server-Sent Events）生成大纲

---

## ✨ 项目亮点

* 🔗 **A2A + ADK 无缝集成**：实现多智能体任务执行。
* 📄 **标准化 XML PPT 输出**：支持基于结构化大纲生成内容丰富、布局多样的 XML 演示文稿。
* 🌐 **流式响应支持（SSE）**：支持客户端实时接收 Agent 执行状态及内容更新。

---

## 📂 项目结构说明

| 文件                      | 说明                                |
| ----------------------- | --------------------------------- |
| `a2a_client.py`         | A2A 客户端封装，管理与服务端的交互               |
| `agent.py`              | 主控制 Agent，调度执行任务                  |
| `main_api.py`           | FastAPI 主入口，暴露 SSE 接口             |
| `adk_agent_executor.py` | 实现 A2A 与 ADK 框架的集成逻辑              |
| `.env`                  | 环境变量配置，需包含 Google GenAI API 密钥等信息 |

---

## 📌 示例：主题生成任务

以下是可以测试的典型主题输入，系统将自动生成结构化内容：

* 电动汽车市场调研

---

## 运行方法
```
在.env文件中先修改模型和模型使用的key
python main_api.py
```


a2a_client.py 输出结果示例:
```
/Users/<USER>/miniforge3/envs/a2a/bin/python a2a_client.py 
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'state': 'submitted'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': 'dfb54739-1445-45be-b1cf-e582c87ec818', 'parts': [{'kind': 'text', 'text': 'Okay'}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': '66bea100-fc6d-4806-b8a9-c2faec626bf4', 'parts': [{'kind': 'text', 'text': ", here's an outline for a Tesla Car Investigation. This outline could be"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': 'd12e10df-a838-4ed7-974d-2de69ee05340', 'parts': [{'kind': 'text', 'text': ' used for a news report, a research paper, or even a fictional story. I'}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': '60e2ce89-9e24-4d04-98e7-c7fa5f373ed4', 'parts': [{'kind': 'text', 'text': "'ve tried to cover a range of potential angles.\n\n# Overview of Tesla Cars\n- History of Tesla and its impact on the automotive industry\n- Different"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': '789a1a5a-ba90-464b-985f-a1fb3f874196', 'parts': [{'kind': 'text', 'text': ' Tesla models and their key features (Model S, 3, X, Y, Cybertruck, etc.)\n- General technological innovations in Tesla vehicles (battery'}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': '13f1a605-982d-4eb6-8478-2dcb113e2b81', 'parts': [{'kind': 'text', 'text': ' tech, Autopilot, etc.)\n\n# Safety Concerns and Investigations\n- Autopilot and Full Self-Driving (FSD) controversies\n  - History of Autopilot accidents and investigations\n  - Debate over the safety and reliability of Autopilot'}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': 'b7940a1a-7c1e-4edb-a4cc-1d4830811b92', 'parts': [{'kind': 'text', 'text': '/FSD\n  - Regulatory scrutiny from NHTSA, NTSB, and other agencies\n- Battery fires and safety\n  - Instances of battery fires in Tesla vehicles\n  - Investigation into the cause of these fires\n  -'}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': 'e6bf93a0-8f7f-4105-8e7c-8b8f1962f566', 'parts': [{'kind': 'text', 'text': " Tesla's response and safety improvements\n- Other safety-related issues\n  - Suspension problems, sudden unintended acceleration, etc.\n  - Recalls and safety updates issued by Tesla\n\n# Technology and Innovation\n- Deep dive into Tesla's battery technology\n  - Battery chemistry, energy density, and lifespan\n  -"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': '8e342058-3c6e-48ba-aeea-c994ecf20f7f', 'parts': [{'kind': 'text', 'text': " Tesla's battery manufacturing (Gigafactory)\n- Autopilot and Full Self-Driving (FSD)\n  - How Autopilot/FSD works: sensors, AI, software\n  - Ethical considerations of autonomous driving\n  - Future development of self-driving technology\n- Over-the-air"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': 'b63bcae6-6c06-4809-b143-85f11beafd50', 'parts': [{'kind': 'text', 'text': " software updates\n  - How Tesla uses software updates to improve its cars\n  - Impact of software updates on vehicle performance and features\n  - Potential risks and benefits of frequent software updates\n\n# Production and Manufacturing\n- Tesla's manufacturing process\n  - Overview of Tesla's factories (Gigafactory locations)\n  "}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': 'dfc13533-0dc6-46fa-baed-6bfba9598098', 'parts': [{'kind': 'text', 'text': "- Manufacturing challenges and bottlenecks\n  - Quality control issues and concerns\n- Supply chain and sourcing of materials\n  - Sourcing of battery materials (lithium, nickel, cobalt)\n  - Ethical and environmental concerns related to mining\n  - Tesla's efforts to secure its supply chain\n\n# Environmental Impact\n- Electric"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': '11dbb798-1567-47a4-bbe1-02ac8f16b6f4', 'parts': [{'kind': 'text', 'text': " vehicles and sustainability\n  - Environmental benefits of electric vehicles compared to gasoline cars\n  - Lifecycle emissions analysis of Tesla vehicles\n- Battery recycling and disposal\n  - Challenges of recycling lithium-ion batteries\n  - Tesla's battery recycling program\n  - Environmental impact of battery disposal\n\n# Financial and Market Analysis\n-"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': 'c53ebe55-29e5-4fd9-aee4-be54400430d8', 'parts': [{'kind': 'text', 'text': " Tesla's financial performance\n  - Revenue, profit, and market capitalization\n  - Investment in research and development\n- Market position and competition\n  - Tesla's market share in the electric vehicle market\n  - Competition from other electric vehicle manufacturers (legacy automakers and startups)\n- Stock performance and investor sentiment\n  -"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': '2f1b7f92-019b-436c-a5cd-a0f52c90b9ea', 'parts': [{'kind': 'text', 'text': " Analysis of Tesla's stock price and volatility\n  - Investor confidence in Tesla's future\n\n# Ethical and Social Implications\n- Elon Musk's influence and leadership\n  - Impact of Elon Musk's personality and decisions on Tesla\n  - Controversies and public perception of Elon Musk\n- Labor practices and"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': '0516a7b2-6a3a-445c-85f2-1aa67e45c38a', 'parts': [{'kind': 'text', 'text': ' working conditions\n  - Allegations of poor working conditions at Tesla factories\n  - Unionization efforts and worker rights\n- Data privacy and security\n  - Collection and use of vehicle data\n  - Security vulnerabilities and hacking risks\n\n# The Future of Tesla\n- Upcoming models and innovations\n  - Cybertruck,'}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': 'cd0b8ea0-7c6c-40e8-8fe1-e6c8e4ff287e', 'parts': [{'kind': 'text', 'text': " Roadster, and other future vehicles\n  - New technologies and features in development\n- Tesla's role in the future of transportation\n  - Impact of Tesla on the automotive industry\n  - Vision for sustainable transportation and energy\n- Challenges and opportunities for Tesla\n  - Competition, regulation, and technological advancements\n  -"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': False, 'kind': 'status-update', 'status': {'message': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'message', 'messageId': '1bf6cb97-10e8-4a1a-b648-a52ff191aada', 'parts': [{'kind': 'text', 'text': " Potential risks and rewards for Tesla's future success\n"}], 'role': 'agent', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}, 'state': 'working'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'artifact': {'artifactId': 'b066b569-91a9-4ad9-ac3f-376ba535c845', 'parts': [{'kind': 'text', 'text': "Okay, here's an outline for a Tesla Car Investigation. This outline could be used for a news report, a research paper, or even a fictional story. I've tried to cover a range of potential angles.\n\n# Overview of Tesla Cars\n- History of Tesla and its impact on the automotive industry\n- Different Tesla models and their key features (Model S, 3, X, Y, Cybertruck, etc.)\n- General technological innovations in Tesla vehicles (battery tech, Autopilot, etc.)\n\n# Safety Concerns and Investigations\n- Autopilot and Full Self-Driving (FSD) controversies\n  - History of Autopilot accidents and investigations\n  - Debate over the safety and reliability of Autopilot/FSD\n  - Regulatory scrutiny from NHTSA, NTSB, and other agencies\n- Battery fires and safety\n  - Instances of battery fires in Tesla vehicles\n  - Investigation into the cause of these fires\n  - Tesla's response and safety improvements\n- Other safety-related issues\n  - Suspension problems, sudden unintended acceleration, etc.\n  - Recalls and safety updates issued by Tesla\n\n# Technology and Innovation\n- Deep dive into Tesla's battery technology\n  - Battery chemistry, energy density, and lifespan\n  - Tesla's battery manufacturing (Gigafactory)\n- Autopilot and Full Self-Driving (FSD)\n  - How Autopilot/FSD works: sensors, AI, software\n  - Ethical considerations of autonomous driving\n  - Future development of self-driving technology\n- Over-the-air software updates\n  - How Tesla uses software updates to improve its cars\n  - Impact of software updates on vehicle performance and features\n  - Potential risks and benefits of frequent software updates\n\n# Production and Manufacturing\n- Tesla's manufacturing process\n  - Overview of Tesla's factories (Gigafactory locations)\n  - Manufacturing challenges and bottlenecks\n  - Quality control issues and concerns\n- Supply chain and sourcing of materials\n  - Sourcing of battery materials (lithium, nickel, cobalt)\n  - Ethical and environmental concerns related to mining\n  - Tesla's efforts to secure its supply chain\n\n# Environmental Impact\n- Electric vehicles and sustainability\n  - Environmental benefits of electric vehicles compared to gasoline cars\n  - Lifecycle emissions analysis of Tesla vehicles\n- Battery recycling and disposal\n  - Challenges of recycling lithium-ion batteries\n  - Tesla's battery recycling program\n  - Environmental impact of battery disposal\n\n# Financial and Market Analysis\n- Tesla's financial performance\n  - Revenue, profit, and market capitalization\n  - Investment in research and development\n- Market position and competition\n  - Tesla's market share in the electric vehicle market\n  - Competition from other electric vehicle manufacturers (legacy automakers and startups)\n- Stock performance and investor sentiment\n  - Analysis of Tesla's stock price and volatility\n  - Investor confidence in Tesla's future\n\n# Ethical and Social Implications\n- Elon Musk's influence and leadership\n  - Impact of Elon Musk's personality and decisions on Tesla\n  - Controversies and public perception of Elon Musk\n- Labor practices and working conditions\n  - Allegations of poor working conditions at Tesla factories\n  - Unionization efforts and worker rights\n- Data privacy and security\n  - Collection and use of vehicle data\n  - Security vulnerabilities and hacking risks\n\n# The Future of Tesla\n- Upcoming models and innovations\n  - Cybertruck, Roadster, and other future vehicles\n  - New technologies and features in development\n- Tesla's role in the future of transportation\n  - Impact of Tesla on the automotive industry\n  - Vision for sustainable transportation and energy\n- Challenges and opportunities for Tesla\n  - Competition, regulation, and technological advancements\n  - Potential risks and rewards for Tesla's future success\n"}]}, 'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'kind': 'artifact-update', 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}
{'id': '00857233-1927-4cd4-ab2d-4714b166967b', 'jsonrpc': '2.0', 'result': {'contextId': 'f9788c09-9cf5-44f3-8f00-aacb4199a0e6', 'final': True, 'kind': 'status-update', 'status': {'state': 'completed'}, 'taskId': 'd9982ce3-f0de-4901-a21f-250d27448bf1'}}

Process finished with exit code 0
```

# 对于携带metadata的数据，它的数据流动方式过程是，底层metadata元数据的流动过程。
1. a2a_client.py ，payload中携带metadata,例如send_message_payload中

2. adk_agent_executor.py中的async def execute的context.message.metadata获取到元数据，async def _process_request的await self._upsert_session在创建新的session时,把元数据放到state中，app_name=self.runner.app_name, user_id="self", session_id=session_id, state={"metadata":metadata}

3. agent.py的before_model_callback中可以看到元数据, callback_context.state.get("metadata")

4. 如果使用了工具，那么tool_context中的tool_context.state.get("metadata")可以获取到元数据，

5. 工具调用时的原信息可以附近到metadata中， metadata["tool_document_ids"] = document_ids
    tool_context.state["metadata"] = metadata

6. agent.py的after_model_callback可以看到metadata = callback_context.state.get("metadata")信息，包括工具更新的结果。

7. adk_agent_executor.py中中的async def _process_request的if event.is_final_response():中获取final_session = await self.runner.session_service.get_session(
                    app_name=self.runner.app_name, user_id="self", session_id=session_id
                )
                print("最终的session中的结果final_session中的state: ", final_session.state)
8. 最终的客户端打印出metadata, {'id': '57071402-98d3-459d-a447-cbdf384e8323', 'jsonrpc': '2.0', 'result': {'artifact': {'artifactId': '156b98ac-3aa6-412c-97e2-6dff3148c46b', 'metadata': {'user_data': 'hello', 'tool_document_ids': [0, 1, 2, 3]}, 'parts': [{'kind': 'text', 'text': '# 电动汽车全球市场概况\n- 全球销量持续增长，新兴市场表现突出\n- 中国引领全球电动汽车市场\n- 欧美市场增长放缓，面临挑战\n\n# 电动汽车技术与成本\n- 电池技术进步与成本下降\n- 充电基础设施的完善与创新\n- 电动卡车经济性改善，长途运输潜力显现\n\n# 电动汽车的消费者接受度\n- 消费者对电动汽车的接受度存在分化\n- 政策与补贴对消费者购买意愿的影响\n- 续航里程和电池衰减是消费者关注的重点'}]}, 'contextId': 'cdbf96d6-8a35-40e2-b3cd-e026c2c446f1', 'kind': 'artifact-update', 'taskId': '3d5160e2-42ef-4244-872f-e23046b685f1'}}