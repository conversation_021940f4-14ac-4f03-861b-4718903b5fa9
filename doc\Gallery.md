#  前端API封装和调用
  1. 前端：API 服务层抽象

   - 关注点： 将API请求逻辑与UI组件逻辑分离。
   - 项目中的体现：
       - src/services/index.ts 文件扮演了“API服务层”的角色。它将所有与后端通信的 fetch 请求封装成独立的、可复用的函数（如
         AIPPT_Outline）。
       - UI组件（如 src/views/Editor/AIPPTDialog.vue）不直接调用 fetch，而是调用 api.AIPPT_Outline() 这个更语义化的函数。
   - 学习价值： 这样做的好处是，如果未来API的URL、请求方法或参数发生变化，您只需要修改 services/index.ts
     这一个文件，而不需要去改动每一个调用它的组件。这使得代码更易于维护。

  2. 开发环境：利用代理（Proxy）解决跨域问题

   - 关注点： 理解为什么需要代理，以及它的工作原理。
   - 项目中的体现：
       - vite.config.ts 中的 server.proxy 配置是关键。
       - 逻辑（Why）： 在开发时，前端运行在 localhost:5173，而后端在另一个地址（本项目中是
         server.pptist.cn）。浏览器出于安全策略（同源策略）会禁止前端直接向不同源的后端发送请求。
       - 实现（How）： 代理服务器（Vite Dev Server）作为中间人。前端的请求实际上是发给同源的Vite服务器，Vite服务器再把这个请求转发
         给真实的后端，然后将后端的响应再传回给前端。这样就绕过了浏览器的跨域限制。
   - 学习价值： 这是现代前端开发必备的知识点。几乎所有项目在开发阶段都会用到类似的代理机制。

  3. 前后端数据交互：流式传输（Streaming）

   - 关注点： 处理长时间运行的AI生成任务。
   - 项目中的体现：
       - 在 AIPPTDialog.vue 中，代码没有等待一个完整的JSON响应，而是使用了 stream.body.getReader() 和 TextDecoder。
       - 逻辑（Why）： AI生成大纲可能需要几秒甚至更长时间。如果等待它全部生成完再返回，用户会看到很长时间的加载动画。流式传输可以让
         后端每生成一小部分内容，就立刻发送给前端。
       - 实现（How）：
         前端通过循环读取流（reader.read()），实时地将接收到的数据片段解码并追加到界面上，给用户一种“实时打字”的体验。
   - 学习价值： 对于AI应用或任何需要等待长时间处理的请求，流式响应是提升用户体验的关键技术。

  4. 生产环境的差异

   - 关注点： 理解开发环境的代理只是一个“临时方案”。
   - 逻辑（Why）： 在生产环境中，您不会再运行Vite开发服务器。
   - 可能的部署方案：
       1. 同源部署： 将前端的静态文件（HTML, JS, CSS）和后端API部署在同一个域名下。例如，前端文件放在Nginx的某个目录下，同时Nginx将
          /api 的请求反向代理到后端的服务上。
       2. CORS配置：
          如果前后端必须部署在不同域名，那么后端服务器必须正确配置CORS（跨源资源共享）响应头，明确允许来自前端域名的请求。
   - 学习价值： 帮助您建立完整的项目部署观念，而不仅仅停留在开发阶段。

# AI生成PPT的过程
这个过程分为前端用户交互和后端AI处理两个主要部分，二者通过API请求连接
   1. 用户输入主题 (前端)
       * 位置: src/views/Editor/AIPPTDialog.vue
       * 过程: 用户在这个对话框组件中输入一个PPT主题，例如“人工智能的历史”。

   2. 发起API请求 (前端)
       * 位置: src/views/Editor/AIPPTDialog.vue 调用 src/services/index.ts
       * 过程:
           * 当用户点击生成按钮后，AIPPTDialog.vue 中的代码会调用 AIPPT_Outline 函数。
           * 这个函数定义在 src/services/index.ts 中，它的作用是向后端的 /api/tools/aippt_outline 地址发送一个HTTP
             POST请求，并将用户输入的主题作为数据一并发送。

   3. 请求转发 (构建工具)
       * 位置: vite.config.ts
       * 过程: vite.config.ts 文件中的 proxy 配置起到了关键作用。它告诉开发服务器，所有发往 /api
         路径的请求，都应该被自动转发到真正的后端服务器 https://server.pptist.cn，并且在转发时去掉 /api
         前缀。所以，请求的最终目的地是 https://server.pptist.cn/tools/aippt_outline。

   4. AI生成大纲 (后端)
       * 位置: server.pptist.cn 服务器 (代码不在此项目中)
       * 过程: 后端服务器在收到请求后，会调用其内部的AI逻辑（很可能是调用一个大型语言模型），将前端发送的“主题”作为输入，生成一份完
         整的PPT大纲。

   5. 返回并展示结果 (前端)
       * 过程: 后端将生成好的大纲数据返回给前端。AIPPTDialog.vue 组件接收到这些数据后，将其渲染并展示在界面上，供用户确认或修改。
